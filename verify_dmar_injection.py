#!/usr/bin/env python3
"""
DMAR表注入验证脚本
用于验证OpenCore是否成功注入了修改后的DMAR表
"""

import os
import sys
import subprocess
import struct

def check_platform():
    """检查运行平台"""
    if sys.platform.startswith('win'):
        return 'windows'
    elif sys.platform.startswith('linux'):
        return 'linux'
    elif sys.platform.startswith('darwin'):
        return 'macos'
    else:
        return 'unknown'

def verify_dmar_linux():
    """Linux系统下验证DMAR表"""
    print("Linux系统 - 验证DMAR表注入...")
    
    try:
        # 检查DMAR表是否存在
        dmar_path = '/sys/firmware/acpi/tables/DMAR'
        if not os.path.exists(dmar_path):
            print("❌ 错误：未找到DMAR表文件")
            return False
        
        # 读取DMAR表内容
        with open(dmar_path, 'rb') as f:
            dmar_data = f.read()
        
        print(f"✅ DMAR表大小: {len(dmar_data)} 字节")
        
        # 分析表头
        if len(dmar_data) >= 36:
            signature = dmar_data[0:4].decode('ascii', errors='ignore')
            length = struct.unpack('<I', dmar_data[4:8])[0]
            revision = dmar_data[8]
            checksum = dmar_data[9]
            
            print(f"   签名: {signature}")
            print(f"   长度: {length} 字节")
            print(f"   版本: {revision}")
            print(f"   校验和: 0x{checksum:02X}")
            
            # 验证校验和
            calculated_checksum = sum(dmar_data) & 0xFF
            if calculated_checksum == 0:
                print("✅ 校验和验证通过")
            else:
                print("❌ 校验和验证失败")
            
            # 检查是否为我们的修改版本
            if length == 80:
                print("📋 检测到版本1 (保守修改版)")
            elif length == 72:
                print("📋 检测到版本2 (中等修改版)")
            elif length == 64:
                print("📋 检测到版本3 (激进修改版)")
            else:
                print(f"📋 检测到其他版本 (长度: {length})")
        
        # 打印十六进制内容
        print("\n十六进制内容:")
        for i in range(0, min(len(dmar_data), 128), 16):
            hex_part = ' '.join(f'{b:02X}' for b in dmar_data[i:i+16])
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in dmar_data[i:i+16])
            print(f"    {i:04X}: {hex_part:<48} // {ascii_part}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_dmar_windows():
    """Windows系统下验证DMAR表"""
    print("Windows系统 - 验证DMAR表注入...")
    print("⚠️  Windows下需要使用第三方工具如RWEverything来查看ACPI表")
    print("   或者重启到Linux系统进行验证")
    
    # 尝试使用wmic命令获取一些信息
    try:
        result = subprocess.run(['wmic', 'computersystem', 'get', 'model'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   系统型号: {result.stdout.strip()}")
    except:
        pass
    
    return False

def verify_dmar_macos():
    """macOS系统下验证DMAR表"""
    print("macOS系统 - 验证DMAR表注入...")
    
    try:
        # 检查系统信息
        result = subprocess.run(['system_profiler', 'SPHardwareDataType'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 系统信息获取成功")
        
        # 检查IOMMU状态
        result = subprocess.run(['dmesg'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            dmesg_output = result.stdout.lower()
            if 'dmar' in dmesg_output or 'iommu' in dmesg_output:
                print("✅ 在系统日志中发现DMAR/IOMMU相关信息")
                # 提取相关行
                for line in result.stdout.split('\n'):
                    if 'dmar' in line.lower() or 'iommu' in line.lower():
                        print(f"   {line.strip()}")
            else:
                print("⚠️  系统日志中未发现DMAR/IOMMU信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_vt_d_status():
    """检查VT-d状态"""
    print("\n检查VT-d相关状态...")
    
    platform = check_platform()
    
    if platform == 'linux':
        try:
            # 检查内核参数
            with open('/proc/cmdline', 'r') as f:
                cmdline = f.read().strip()
            
            if 'intel_iommu=off' in cmdline:
                print("⚠️  检测到 intel_iommu=off 内核参数")
            elif 'intel_iommu=on' in cmdline:
                print("✅ 检测到 intel_iommu=on 内核参数")
            else:
                print("📋 未检测到特定的intel_iommu内核参数")
            
            # 检查IOMMU组
            iommu_groups_path = '/sys/kernel/iommu_groups'
            if os.path.exists(iommu_groups_path):
                groups = os.listdir(iommu_groups_path)
                print(f"✅ 发现 {len(groups)} 个IOMMU组")
            else:
                print("❌ 未发现IOMMU组")
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")

def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "="*60)
    print("DMAR表修改使用说明")
    print("="*60)
    print("1. 文件准备:")
    print("   - 选择一个版本: DMAR_v1_conservative.aml (推荐)")
    print("   - 重命名为: DMAR.aml")
    print("   - 放置到: EFI/OC/ACPI/DMAR.aml")
    print()
    print("2. OpenCore配置:")
    print("   - 使用提供的opencore_config_template.xml")
    print("   - 合并ACPI删除和添加配置到您的config.plist")
    print("   - 设置 DisableIoMapper = False")
    print()
    print("3. BIOS设置:")
    print("   - VT-d: 开启/Enabled")
    print("   - 其他虚拟化相关设置保持默认")
    print()
    print("4. 测试顺序:")
    print("   - 版本1 (保守) → 版本2 (中等) → 版本3 (激进)")
    print("   - 每次修改后重启验证")
    print()
    print("5. 验证方法:")
    print("   - Linux: 运行此脚本")
    print("   - Windows: 使用RWEverything查看ACPI表")
    print("   - macOS: 检查系统日志")

def main():
    """主函数"""
    print("DMAR表注入验证工具")
    print("="*40)
    
    platform = check_platform()
    print(f"检测到平台: {platform}")
    
    success = False
    
    if platform == 'linux':
        success = verify_dmar_linux()
    elif platform == 'windows':
        success = verify_dmar_windows()
    elif platform == 'macos':
        success = verify_dmar_macos()
    else:
        print("❌ 不支持的平台")
    
    # 检查VT-d状态
    if platform in ['linux', 'macos']:
        check_vt_d_status()
    
    # 打印使用说明
    print_usage_instructions()
    
    if success:
        print("\n✅ 验证完成")
    else:
        print("\n⚠️  验证未完全成功，请检查配置")

if __name__ == "__main__":
    main()
