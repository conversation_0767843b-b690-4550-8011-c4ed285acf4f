<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- 
    OpenCore DMAR表注入配置模板
    针对您的VT-d绕过需求定制
    
    使用说明：
    1. 将此配置合并到您现有的config.plist中
    2. 选择一个DMAR版本重命名为DMAR.aml
    3. 放置到EFI/OC/ACPI/目录
    4. 确保BIOS中VT-d开启
    5. 设置DisableIoMapper为False
    -->
    
    <key>ACPI</key>
    <dict>
        <!-- 删除原始DMAR表 -->
        <key>Delete</key>
        <array>
            <dict>
                <key>All</key>
                <true/>
                <key>Comment</key>
                <string>Delete original DMAR table for VT-d bypass</string>
                <key>Enabled</key>
                <true/>
                <key>OemTableId</key>
                <data></data>
                <key>TableLength</key>
                <integer>0</integer>
                <key>TableSignature</key>
                <data>RE1BUg==</data>
            </dict>
        </array>
        
        <!-- 注入修改后的DMAR表 -->
        <key>Add</key>
        <array>
            <dict>
                <key>Comment</key>
                <string>Inject modified DMAR table - Version 1 Conservative</string>
                <key>Enabled</key>
                <true/>
                <key>Path</key>
                <string>DMAR.aml</string>
            </dict>
        </array>
        
        <!-- 其他ACPI设置保持不变 -->
        <key>Patch</key>
        <array>
            <!-- 您现有的ACPI补丁保持不变 -->
        </array>
        
        <key>Quirks</key>
        <dict>
            <!-- 重要：保持这些设置以确保DMAR注入正常工作 -->
            <key>FadtEnableReset</key>
            <false/>
            <key>NormalizeHeaders</key>
            <false/>
            <key>RebaseRegions</key>
            <false/>
            <key>ResetHwSig</key>
            <false/>
            <key>ResetLogoStatus</key>
            <false/>
            <key>SyncTableIds</key>
            <false/>
        </dict>
    </dict>
    
    <key>Kernel</key>
    <dict>
        <key>Quirks</key>
        <dict>
            <!-- 关键设置：必须设置为False以使用自定义DMAR表 -->
            <key>DisableIoMapper</key>
            <false/>
            
            <!-- 其他内核设置根据您的需求调整 -->
            <key>AppleXcpmCfgLock</key>
            <false/>
            <key>DisableLinkeditJettison</key>
            <true/>
            <key>DummyPowerManagement</key>
            <false/>
            <key>ExtendBTFeatureFlags</key>
            <false/>
            <key>ExternalDiskIcons</key>
            <false/>
            <key>IncreasePciBarSize</key>
            <false/>
            <key>PowerTimeoutKernelPanic</key>
            <true/>
            <key>ThirdPartyDrives</key>
            <false/>
            <key>XhciPortLimit</key>
            <false/>
        </dict>
        
        <!-- 其他内核配置保持不变 -->
        <key>Add</key>
        <array>
            <!-- 您现有的内核扩展 -->
        </array>
        
        <key>Block</key>
        <array>
            <!-- 您现有的内核扩展阻止列表 -->
        </array>
        
        <key>Emulate</key>
        <dict>
            <!-- CPU仿真设置 -->
        </dict>
        
        <key>Force</key>
        <array>
            <!-- 强制加载的内核扩展 -->
        </array>
        
        <key>Patch</key>
        <array>
            <!-- 内核补丁 -->
        </array>
        
        <key>Scheme</key>
        <dict>
            <key>FuzzyMatch</key>
            <true/>
            <key>KernelArch</key>
            <string>x86_64</string>
            <key>KernelCache</key>
            <string>Auto</string>
        </dict>
    </dict>
    
    <!-- 调试设置（可选，用于验证DMAR注入） -->
    <key>Misc</key>
    <dict>
        <key>Debug</key>
        <dict>
            <key>AppleDebug</key>
            <true/>
            <key>ApplePanic</key>
            <true/>
            <key>DisableWatchDog</key>
            <false/>
            <key>DisplayDelay</key>
            <integer>0</integer>
            <key>DisplayLevel</key>
            <integer>2147483650</integer>
            <key>LogModules</key>
            <string>*</string>
            <key>SerialInit</key>
            <false/>
            <key>SysReport</key>
            <false/>
            <key>Target</key>
            <integer>3</integer>
        </dict>
        
        <!-- 其他Misc设置保持不变 -->
        <key>Entries</key>
        <array>
            <!-- 启动条目 -->
        </array>
        
        <key>Security</key>
        <dict>
            <!-- 安全设置 -->
        </dict>
        
        <key>Tools</key>
        <array>
            <!-- 工具 -->
        </array>
    </dict>
</dict>
</plist>
