#!/usr/bin/env python3
"""
DMAR表生成脚本 - 为您的具体需求定制
基于您的80字节原始DMAR表生成三个修改版本
"""

import struct
import os

def calculate_checksum(data):
    """计算ACPI表校验和"""
    checksum = sum(data) & 0xFF
    return (256 - checksum) & 0xFF

def create_dmar_version1():
    """版本1：保守修改版 - 保持原有结构，仅优化标志位"""
    
    # ACPI表头 (36字节)
    header = bytearray([
        0x44, 0x4D, 0x41, 0x52,  # Signature "DMAR"
        0x50, 0x00, 0x00, 0x00,  # Length: 80 bytes
        0x01,                     # Revision
        0x00,                     # Checksum (待计算)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20,  # OEM ID "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20,  # OEM Table ID "EDK2    "
        0x02, 0x00, 0x00, 0x00,  # OEM Revision
        0x20, 0x20, 0x20, 0x20,  # ASL Compiler ID
        0x13, 0x00, 0x00, 0x01   # ASL Compiler Revision
    ])
    
    # DMAR头扩展 (12字节)
    dmar_header = bytearray([
        0x26,  # Host Address Width
        0x01,  # Flags (INTR_REMAP enabled)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  # Reserved
    ])
    
    # DHRD条目 (32字节)
    dhrd_entry = bytearray([
        0x00, 0x00,  # Type: Hardware Unit Definition
        0x20, 0x00,  # Length: 32 bytes
        0x01,        # Flags: INCLUDE_PCI_ALL
        0x00,        # Size
        0x00, 0x00,  # PCI Segment Number
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00,  # Register Base Address: FED91000
        
        # IOAPIC设备作用域
        0x03, 0x08, 0x00, 0x00, 0x02, 0x00, 0x1E, 0x07,
        
        # HPET设备作用域  
        0x04, 0x08, 0x00, 0x00, 0x00, 0x00, 0x1E, 0x06
    ])
    
    # 组合完整表
    full_table = header + dmar_header + dhrd_entry
    
    # 计算并设置校验和
    full_table[9] = calculate_checksum(full_table)
    
    return full_table

def create_dmar_version2():
    """版本2：中等修改版 - 移除HPET设备作用域"""
    
    # ACPI表头 (36字节) - 长度改为72
    header = bytearray([
        0x44, 0x4D, 0x41, 0x52,  # Signature "DMAR"
        0x48, 0x00, 0x00, 0x00,  # Length: 72 bytes
        0x01,                     # Revision
        0x00,                     # Checksum (待计算)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20,  # OEM ID "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20,  # OEM Table ID "EDK2    "
        0x02, 0x00, 0x00, 0x00,  # OEM Revision
        0x20, 0x20, 0x20, 0x20,  # ASL Compiler ID
        0x13, 0x00, 0x00, 0x01   # ASL Compiler Revision
    ])
    
    # DMAR头扩展 (12字节)
    dmar_header = bytearray([
        0x26,  # Host Address Width
        0x01,  # Flags (INTR_REMAP enabled)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  # Reserved
    ])
    
    # DHRD条目 (24字节) - 移除HPET
    dhrd_entry = bytearray([
        0x00, 0x00,  # Type: Hardware Unit Definition
        0x18, 0x00,  # Length: 24 bytes
        0x01,        # Flags: INCLUDE_PCI_ALL
        0x00,        # Size
        0x00, 0x00,  # PCI Segment Number
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00,  # Register Base Address: FED91000
        
        # 仅保留IOAPIC设备作用域
        0x03, 0x08, 0x00, 0x00, 0x02, 0x00, 0x1E, 0x07
    ])
    
    # 组合完整表
    full_table = header + dmar_header + dhrd_entry
    
    # 计算并设置校验和
    full_table[9] = calculate_checksum(full_table)
    
    return full_table

def create_dmar_version3():
    """版本3：PCIe DMA卡专用版 - 正确的最小DHRD结构"""

    # ACPI表头 (36字节) - 长度改为64
    header = bytearray([
        0x44, 0x4D, 0x41, 0x52,  # Signature "DMAR"
        0x40, 0x00, 0x00, 0x00,  # Length: 64 bytes (修正)
        0x01,                     # Revision
        0x00,                     # Checksum (待计算)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20,  # OEM ID "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20,  # OEM Table ID "EDK2    "
        0x02, 0x00, 0x00, 0x00,  # OEM Revision
        0x20, 0x20, 0x20, 0x20,  # ASL Compiler ID
        0x13, 0x00, 0x00, 0x01   # ASL Compiler Revision
    ])

    # DMAR头扩展 (12字节)
    dmar_header = bytearray([
        0x26,  # Host Address Width
        0x01,  # Flags (INTR_REMAP enabled)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  # Reserved
    ])

    # DHRD条目 (16字节) - 完整结构但无设备作用域
    dhrd_entry = bytearray([
        0x00, 0x00,  # Type: Hardware Unit Definition
        0x10, 0x00,  # Length: 16 bytes (修正：必须包含寄存器基址)
        0x01,        # Flags: INCLUDE_PCI_ALL (关键：包含所有PCI设备)
        0x00,        # Size (0 = 4KB pages)
        0x00, 0x00,  # PCI Segment Number
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00   # Register Base Address: FED91000
    ])

    # 组合完整表
    full_table = header + dmar_header + dhrd_entry

    # 计算并设置校验和
    full_table[9] = calculate_checksum(full_table)

    return full_table

def create_dmar_version4_pcie_bypass():
    """版本4：PCIe DMA卡专用绕过版 - 添加RMRR保留内存区域"""

    # 计算实际长度：36(header) + 12(dmar_header) + 16(dhrd) + 24(rmrr) = 88字节
    # ACPI表头 (36字节) - 修正长度为88字节
    header = bytearray([
        0x44, 0x4D, 0x41, 0x52,  # Signature "DMAR"
        0x58, 0x00, 0x00, 0x00,  # Length: 88 bytes (修正)
        0x01,                     # Revision
        0x00,                     # Checksum (待计算)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20,  # OEM ID "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20,  # OEM Table ID "EDK2    "
        0x02, 0x00, 0x00, 0x00,  # OEM Revision
        0x20, 0x20, 0x20, 0x20,  # ASL Compiler ID
        0x13, 0x00, 0x00, 0x01   # ASL Compiler Revision
    ])

    # DMAR头扩展 (12字节)
    dmar_header = bytearray([
        0x26,  # Host Address Width
        0x01,  # Flags (INTR_REMAP enabled)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  # Reserved
    ])

    # DHRD条目 (16字节) - 最小配置
    dhrd_entry = bytearray([
        0x00, 0x00,  # Type: Hardware Unit Definition
        0x10, 0x00,  # Length: 16 bytes
        0x00,        # Flags: 0 (不包含所有PCI设备，只包含明确指定的)
        0x00,        # Size
        0x00, 0x00,  # PCI Segment Number
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00   # Register Base Address: FED91000
    ])

    # RMRR条目 (24字节) - 修正长度，保留内存区域
    rmrr_entry = bytearray([
        0x01, 0x00,  # Type: Reserved Memory Region Reporting
        0x18, 0x00,  # Length: 24 bytes (修正)
        0x00, 0x00,  # Reserved
        0x00, 0x00,  # PCI Segment Number
        0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00,  # Base Address: 0x80000000 (2GB)
        0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00   # Limit Address: 0xFFFFFFFF (4GB-1)
        # 无设备作用域条目 - 所有设备都可以访问这个保留区域
    ])

    # 组合完整表
    full_table = header + dmar_header + dhrd_entry + rmrr_entry

    # 计算并设置校验和
    full_table[9] = calculate_checksum(full_table)

    return full_table

def create_dmar_version5_simple_bypass():
    """版本5：最简单的PCIe DMA绕过版 - 禁用INCLUDE_PCI_ALL"""

    # ACPI表头 (36字节) - 长度为64字节
    header = bytearray([
        0x44, 0x4D, 0x41, 0x52,  # Signature "DMAR"
        0x40, 0x00, 0x00, 0x00,  # Length: 64 bytes
        0x01,                     # Revision
        0x00,                     # Checksum (待计算)
        0x49, 0x4E, 0x54, 0x45, 0x4C, 0x20,  # OEM ID "INTEL "
        0x45, 0x44, 0x4B, 0x32, 0x20, 0x20, 0x20, 0x20,  # OEM Table ID "EDK2    "
        0x02, 0x00, 0x00, 0x00,  # OEM Revision
        0x20, 0x20, 0x20, 0x20,  # ASL Compiler ID
        0x13, 0x00, 0x00, 0x01   # ASL Compiler Revision
    ])

    # DMAR头扩展 (12字节)
    dmar_header = bytearray([
        0x26,  # Host Address Width
        0x01,  # Flags (INTR_REMAP enabled)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  # Reserved
    ])

    # DHRD条目 (16字节) - 关键：Flags=0，不包含任何PCI设备
    dhrd_entry = bytearray([
        0x00, 0x00,  # Type: Hardware Unit Definition
        0x10, 0x00,  # Length: 16 bytes
        0x00,        # Flags: 0 (关键：不包含任何PCI设备)
        0x00,        # Size
        0x00, 0x00,  # PCI Segment Number
        0x00, 0x10, 0xD9, 0xFE, 0x00, 0x00, 0x00, 0x00   # Register Base Address: FED91000
    ])

    # 组合完整表
    full_table = header + dmar_header + dhrd_entry

    # 计算并设置校验和
    full_table[9] = calculate_checksum(full_table)

    return full_table

def save_binary_file(filename, data):
    """保存二进制文件"""
    with open(filename, 'wb') as f:
        f.write(data)
    print(f"已创建文件: {filename} ({len(data)} 字节)")

def print_hex_dump(data, filename):
    """打印十六进制转储用于验证"""
    print(f"\n{filename} 十六进制内容:")
    for i in range(0, len(data), 16):
        hex_part = ' '.join(f'{b:02X}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        print(f"    {i:04X}: {hex_part:<48} // {ascii_part}")

def main():
    """主函数 - 生成所有版本的DMAR表"""

    print("正在生成修改后的DMAR表文件...")
    print("基于您的原始80字节DMAR表 (Register Base: FED91000)")
    print("专门针对PCIe DMA卡绕过VT-d保护")
    print("=" * 60)

    # 生成版本1：保守修改
    version1_data = create_dmar_version1()
    save_binary_file("DMAR_v1_conservative.aml", version1_data)
    print_hex_dump(version1_data, "版本1 (保守修改)")

    print("\n" + "=" * 60)

    # 生成版本2：中等修改
    version2_data = create_dmar_version2()
    save_binary_file("DMAR_v2_moderate.aml", version2_data)
    print_hex_dump(version2_data, "版本2 (中等修改)")

    print("\n" + "=" * 60)

    # 生成版本3：修正的激进修改
    version3_data = create_dmar_version3()
    save_binary_file("DMAR_v3_fixed.aml", version3_data)
    print_hex_dump(version3_data, "版本3 (修正的激进修改)")

    print("\n" + "=" * 60)

    # 生成版本4：PCIe DMA专用
    version4_data = create_dmar_version4_pcie_bypass()
    save_binary_file("DMAR_v4_pcie_bypass.aml", version4_data)
    print_hex_dump(version4_data, "版本4 (PCIe DMA专用绕过)")

    print("\n" + "=" * 60)
    print("PCIe DMA卡使用说明:")
    print("1. 推荐版本4 (RMRR保留内存区域方案)")
    print("2. 版本3 (修正后的最小DHRD结构)")
    print("3. 放置选择的版本到 EFI/OC/ACPI/DMAR.aml")
    print("4. 配置 config.plist 的 ACPI 删除和添加条目")
    print("5. 确保 BIOS 中 VT-d 设置为开启")
    print("6. 设置 DisableIoMapper = False")
    print("\n针对PCIe DMA卡推荐测试顺序: v4 → v3 → v2")
    print("版本4通过RMRR为DMA操作保留大内存区域")
    print("版本3通过最小DHRD减少IOMMU保护规则")

if __name__ == "__main__":
    main()
